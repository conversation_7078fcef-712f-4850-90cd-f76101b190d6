/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "mlx90393.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// MLX90393相关变量
static MLX90393_Handle_t mlx_handle;
static uint8_t mlx90393_initialized = 0;
volatile uint8_t mlx90393_restart_flag = 0; // 重启标志位
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  /* USER CODE BEGIN 2 */
	// ==================== ADC电压校准模式 ====================
	printf("=== ADC Battery Voltage Calibration Mode ===\r\n");
	printf("Use multimeter to measure actual battery voltage\r\n");
	printf("Compare with displayed values for calibration\r\n");
	printf("Format: Raw | VRef | VDD | Voltage\r\n");
	printf("==========================================\r\n");

	// ==================== MLX90393磁场传感器初始化 ====================
	printf("\r\n=== MLX90393 Initialization ===\r\n");

	// 上电MLX90393
	MLX90393_PW_ON;
	HAL_Delay(100); // 等待上电稳定

	// 初始化MLX90393
	if (MLX90393_Init(&mlx_handle, &hi2c1) == MLX90393_OK) {
		printf("MLX90393 Init: SUCCESS\r\n");
		mlx90393_initialized = 1;

		// 配置WOC模式（唤醒变化模式）
		if (MLX90393_ConfigureWOC(&mlx_handle) == MLX90393_OK) {
			printf("MLX90393 WOC Mode: ENABLED\r\n");
			printf("INT pin (PB0) ready for interrupts\r\n");
			printf("Threshold: XY=%d, Z=%d LSB\r\n", MLX90393_WOC_XY_THRESHOLD, MLX90393_WOC_Z_THRESHOLD);
		} else {
			printf("MLX90393 WOC Config: FAILED\r\n");
		}
	} else {
		printf("MLX90393 Init: FAILED\r\n");
		mlx90393_initialized = 0;
	}

	// ==================== E70通信模块（暂时注释） ====================
	/*
	uint8_t init_result = E70_InitializeConfig(E70_MODULE_ADDRESS, 10, 1);
	if (!init_result) {
		printf("E70 Init FAILED!\r\n");
		Error_Handler();
	}
	E70_EnterCommMode();
	E70_StartContinuousRx();
	printf("=== E70 Ready for Communication ===\r\n");
	*/

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    // 检查MLX90393重启标志位
    if (mlx90393_restart_flag) {
        mlx90393_restart_flag = 0; // 清除标志位
        MLX90393_Restart(); // 执行重启
    }

    LED_TOGGLE;

    // 读取电池电压并持续刷新显示
    uint16_t battery_voltage_mv = ADC_ReadBatteryVoltage();
    float battery_voltage_v = battery_voltage_mv / 1000.0f;
    uint32_t current_time = HAL_GetTick();

    // 电压测量输出
//    printf("\r\nBattery: %.3fV (%dmV) ",battery_voltage_v, battery_voltage_mv);

    // MLX90393状态显示
    if (mlx90393_initialized) {
        printf("\r\nMLX90393: WOC Mode Active");
    } else {
        printf("\r\nMLX90393: Not Ready");
    }

    // ==================== E70通信功能（暂时注释） ====================
    /*
    if (current_time - last_send_time >= 5000) {
        last_send_time = current_time;
        uint16_t battery_voltage = ADC_ReadBatteryVoltage();
        E70_SendDeviceInfo(MCU_DEVICE_ID, battery_voltage);
        HAL_Delay(100);
        if (E70_CheckReceiveAck()) {
            uart_rx_index = 0;
            memset((void*)uart_rx_buffer, 0, sizeof(uart_rx_buffer));
            E70_StartContinuousRx();
        }
    }
    */

    HAL_Delay(100);  // 100ms间隔更新

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
  * @brief  重启MLX90393传感器
  * @retval None
  */
void MLX90393_Restart(void)
{
    printf("\r\n=== Restarting MLX90393 ===\r\n");

    // 1. 关闭MLX90393电源
    MLX90393_PW_OFF;
    printf("Power OFF\r\n");
    HAL_Delay(500); // 等待500ms确保完全断电

    // 2. 重新上电
    MLX90393_PW_ON;
    printf("Power ON\r\n");
    HAL_Delay(100); // 等待上电稳定

    // 3. 重新初始化
    if (MLX90393_Init(&mlx_handle, &hi2c1) == MLX90393_OK) {
        printf("MLX90393 Re-Init: SUCCESS\r\n");
        mlx90393_initialized = 1;

        // 4. 重新配置WOC模式
        if (MLX90393_ConfigureWOC(&mlx_handle) == MLX90393_OK) {
            printf("MLX90393 WOC Mode: RE-ENABLED\r\n");
            printf("Ready for next interrupt test\r\n");
        } else {
            printf("MLX90393 WOC Re-Config: FAILED\r\n");
            mlx90393_initialized = 0;
        }
    } else {
        printf("MLX90393 Re-Init: FAILED\r\n");
        mlx90393_initialized = 0;
    }

    printf("=== Restart Complete ===\r\n");
}

/**
  * @brief  测试MLX90393中断功能
  * @retval None
  */
void MLX90393_TestInterrupt(void)
{
    if (!mlx90393_initialized) {
        printf("MLX90393 not initialized!\r\n");
        return;
    }

    printf("\r\n=== MLX90393 Interrupt Test ===\r\n");
    printf("Move a magnet near the sensor to trigger interrupt\r\n");
    printf("Watch for '*** MLX90393 INT Triggered! ***' messages\r\n");
    printf("Sensor will auto-restart after each trigger\r\n");

    // 这里可以添加更多测试逻辑
    // 比如读取当前磁场值作为基准等
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
